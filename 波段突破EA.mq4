//+------------------------------------------------------------------+
//|                                                    波段突破EA.mq4 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property strict

// 外部参数
extern int    统计K线数量 = 10;           // 统计前N根K线
extern double 突破阈值 = 2500;            // 最高点与最低点差值阈值（点数）
extern double 突破距离 = 100;             // 突破距离（点数）
extern double 止损距离 = 144;             // 止损距离（点数）
extern double 默认止盈 = 2000;            // 默认止盈（点数）
extern double 移动止损盈利 = 200;         // 移动止损触发盈利（点数）
extern double 移动止损距离 = 50;          // 移动止损距离（点数）
extern bool   启用移动止损 = true;        // 是否启用移动止损
extern double 手数 = 0.1;                // 交易手数
extern int    魔术号 = 12345;             // 魔术号

// 全局变量
double 前期最高点 = 0;
double 前期最低点 = 0;
int 上次检查柱 = -1;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("波段突破EA已启动");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("波段突破EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 计算前N根K线的最高点和最低点
    计算前期高低点();
    
    // 检查是否满足突破条件
    if((前期最高点 - 前期最低点) >= 突破阈值 * Point)
        return; // 如果差值大于等于阈值，不进行交易
    
    // 检查当前持仓
    int 当前持仓 = 获取当前持仓();
    
    // 如果没有持仓，检查开仓条件
    if(当前持仓 == 0)
    {
        检查开仓条件();
    }
    else
    {
        // 如果有持仓，检查移动止损
        if(启用移动止损)
            检查移动止损();
    }
}

//+------------------------------------------------------------------+
//| 计算前期高低点                                                    |
//+------------------------------------------------------------------+
void 计算前期高低点()
{
    前期最高点 = High[iHighest(NULL, 0, MODE_HIGH, 统计K线数量, 1)];
    前期最低点 = Low[iLowest(NULL, 0, MODE_LOW, 统计K线数量, 1)];
}

//+------------------------------------------------------------------+
//| 获取当前持仓                                                      |
//+------------------------------------------------------------------+
int 获取当前持仓()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == 魔术号)
            {
                if(OrderType() == OP_BUY)
                    return 1; // 多单
                else if(OrderType() == OP_SELL)
                    return -1; // 空单
            }
        }
    }
    return 0; // 无持仓
}

//+------------------------------------------------------------------+
//| 检查开仓条件                                                      |
//+------------------------------------------------------------------+
void 检查开仓条件()
{
    double 当前价格 = Ask;
    
    // 检查做多条件
    if(当前价格 > 前期最高点 + 突破距离 * Point)
    {
        double 止损价 = 前期最低点 - 止损距离 * Point;
        double 止盈价 = 当前价格 + 默认止盈 * Point;
        
        开多单(止损价, 止盈价);
    }
    // 检查做空条件
    else if(当前价格 < 前期最低点 - 突破距离 * Point)
    {
        当前价格 = Bid;
        double 止损价 = 前期最高点 + 止损距离 * Point;
        double 止盈价 = 当前价格 - 默认止盈 * Point;
        
        开空单(止损价, 止盈价);
    }
}

//+------------------------------------------------------------------+
//| 开多单                                                            |
//+------------------------------------------------------------------+
void 开多单(double 止损, double 止盈)
{
    int ticket = OrderSend(Symbol(), OP_BUY, 手数, Ask, 3, 止损, 止盈, 
                          "波段突破多单", 魔术号, 0, clrGreen);
    
    if(ticket > 0)
    {
        Print("开多单成功，订单号：", ticket, " 止损：", 止损, " 止盈：", 止盈);
    }
    else
    {
        Print("开多单失败，错误代码：", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 开空单                                                            |
//+------------------------------------------------------------------+
void 开空单(double 止损, double 止盈)
{
    int ticket = OrderSend(Symbol(), OP_SELL, 手数, Bid, 3, 止损, 止盈, 
                          "波段突破空单", 魔术号, 0, clrRed);
    
    if(ticket > 0)
    {
        Print("开空单成功，订单号：", ticket, " 止损：", 止损, " 止盈：", 止盈);
    }
    else
    {
        Print("开空单失败，错误代码：", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 检查移动止损                                                      |
//+------------------------------------------------------------------+
void 检查移动止损()
{
    // 检查是否有新K线
    if(上次检查柱 == Bars)
        return;
    
    上次检查柱 = Bars;
    
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == 魔术号)
            {
                if(OrderType() == OP_BUY)
                {
                    // 多单移动止损
                    double 上一柱最低点 = Low[1];
                    double 当前盈利 = (Bid - OrderOpenPrice()) / Point;
                    
                    if(当前盈利 >= 移动止损盈利)
                    {
                        double 新止损 = 上一柱最低点 - 移动止损距离 * Point;
                        if(新止损 > OrderStopLoss())
                        {
                            bool result = OrderModify(OrderTicket(), OrderOpenPrice(), 
                                                    新止损, OrderTakeProfit(), 0, clrBlue);
                            if(result)
                                Print("多单移动止损成功，新止损：", 新止损);
                        }
                    }
                }
                else if(OrderType() == OP_SELL)
                {
                    // 空单移动止损
                    double 上一柱最高点 = High[1];
                    double 当前盈利 = (OrderOpenPrice() - Ask) / Point;
                    
                    if(当前盈利 >= 移动止损盈利)
                    {
                        double 新止损 = 上一柱最高点 + 移动止损距离 * Point;
                        if(新止损 < OrderStopLoss() || OrderStopLoss() == 0)
                        {
                            bool result = OrderModify(OrderTicket(), OrderOpenPrice(), 
                                                    新止损, OrderTakeProfit(), 0, clrBlue);
                            if(result)
                                Print("空单移动止损成功，新止损：", 新止损);
                        }
                    }
                }
            }
        }
    }
}
